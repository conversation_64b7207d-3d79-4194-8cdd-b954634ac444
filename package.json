{"name": "unoscribe", "version": "2025.06.10", "private": true, "scripts": {"dev": "next dev", "dev:turbopack": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate --config=drizzle-dev.config.ts", "db:migrate:dev": "drizzle-kit migrate --config=drizzle-dev.config.ts", "db:migrate:turso-prod": "drizzle-kit migrate --config=drizzle-prod.config.ts", "docker:build": "docker compose --file=compose.prod.yml build", "docker:push": "docker push ghcr.io/notlandingstudio/unoscribe-next:${npm_config_tag}"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@aws-sdk/client-s3": "^3.830.0", "@aws-sdk/s3-request-presigner": "^3.830.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@hookform/resolvers": "^4.1.3", "@libsql/client": "^0.15.5", "@lucide/lab": "^0.1.2", "@pdf-lib/fontkit": "^1.1.1", "@polar-sh/checkout": "^0.1.10", "@polar-sh/nextjs": "^0.3.23", "@polar-sh/sdk": "^0.32.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.1.8", "@shadcn/ui": "^0.0.4", "@tiptap-pro/extension-table-of-contents": "^2.18.0-beta.6", "@tiptap/extension-blockquote": "^2.12.0", "@tiptap/extension-bold": "^2.12.0", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-code-block": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-list-item": "^2.12.0", "@tiptap/extension-ordered-list": "^2.12.0", "@tiptap/extension-paragraph": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/html": "^2.11.7", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "ai": "^4.3.16", "better-auth": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "docx": "^9.5.1", "drizzle-orm": "^0.43.1", "embla-carousel-react": "^8.5.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "i18next": "^23.7.12", "i18next-browser-languagedetector": "^7.2.0", "i18next-resources-to-backend": "^1.2.0", "jose": "^6.0.10", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "media-chrome": "^4.11.1", "motion": "^12.6.3", "nanoid": "^5.1.5", "next": "^15.3.3", "next-client-cookies": "^2.0.1", "next-google-adsense": "^1.0.13", "next-i18n-router": "^5.5.1", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "novel": "^1.0.2", "ofetch": "^1.4.1", "openai": "^5.3.0", "pdf-lib": "^1.17.1", "react": "^19.1.0", "react-cookie": "^8.0.1", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-microsoft-clarity": "^2.0.0", "react-use-measure": "^2.1.7", "react-wrap-balancer": "^1.1.1", "redis": "^5.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.2", "superjson": "^2.2.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "url-slug": "^4.0.1", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9.27.0", "eslint-config-next": "15.3.3", "eslint-plugin-react-you-might-not-need-an-effect": "^0.0.43", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.1.6"}}