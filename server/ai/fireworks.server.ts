import { combineWordsToSentences } from "@/lib/utils-transcript";
import { ofetch } from "ofetch";

/**
 * Transcribe audio from a URL via Fireworks
 * @param fileUrl - URL of the audio file to transcribe
 * @param languageCode - Language code for transcription (e.g., 'en', 'zh', 'es')
 */
export async function transcribeFromFireworks(fileUrl: string, languageCode: string): Promise<SentenceData[]> {
	try {
		const { duration, words } = await ofetch("https://audio-turbo.us-virginia-1.direct.fireworks.ai", {
			method: "POST",
			headers: {
				Authorization: process.env.FAL_API_KEY!,
			},
			body: {
				file: fileUrl,
				model: "whisper-v3-turbo",
				alignment_model: languageCode === "en" ? "tdnn_ffn" : "mms_fa",
				language: languageCode,
				response_format: "verbose_json",
				timestamp_granularities: "word",
				diarize: "true",
			},
		});
		const sentences: SentenceData[] = combineWordsToSentences(words);
		return sentences;
	} catch (error: any) {
		console.error("Transcription error:", error);

		if (error.status === 429) {
			throw new Error("Rate limit exceeded: Too many requests");
		}

		// Generic error
		throw new Error(`Transcription error: ${error.message || "Unknown error"}`);
	}
}
